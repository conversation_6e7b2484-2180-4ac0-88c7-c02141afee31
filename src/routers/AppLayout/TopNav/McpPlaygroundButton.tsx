import styled from '@emotion/styled';
import {Tooltip} from 'antd';
import {MCPSquareLink} from '@/links/mcp';
import SvgMcpPlaygroundHomeBtn from '@/assets/mcp/McpPlaygroundHomeBtn';

const StyledLink = styled.a`
    display: flex;
    align-items: center;
    margin-right: 28px;
`;

export const MCPPlaygroundButton = () => {
    return (
        <Tooltip title="试试打造属于你自己的数字员工">
            <StyledLink href={MCPSquareLink.toUrl()} target="_blank" rel="noreferrer">
                <SvgMcpPlaygroundHomeBtn />
            </StyledLink>
        </Tooltip>
    );
};
